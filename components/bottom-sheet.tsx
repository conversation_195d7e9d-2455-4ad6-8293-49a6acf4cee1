import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
} from "react";
import { Dimensions, StyleSheet, View } from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
const { width, height } = Dimensions.get("window");

type BottomSheetprops = {};
export type BottomSheetRefProps = {
  scrollTo: (toValue: number) => void;
};
const BottomSheet = forwardRef<BottomSheetRefProps, BottomSheetprops>(
  ({}, ref) => {
    const translateY = useSharedValue(0);
    const scrollTo = useCallback((toValue: number) => {
      "worklet";
      translateY.value = withSpring(toValue, { damping: 50 });
    }, []);

    useImperativeHandle(
      ref,
      () => ({
        scrollTo,
      }),
      [scrollTo]
    );
    const context = useSharedValue({ y: 0 });
    const MAX_TRANSLATE_Y = -height + 50;
    const panGesture = Gesture.Pan()
      .onStart((event) => {
        context.value = { y: translateY.value };
      })
      .onUpdate((event) => {
        translateY.value = event.translationY + context.value.y;
        translateY.value = Math.max(translateY.value, MAX_TRANSLATE_Y);
      })
      .onEnd((event) => {
        if (translateY.value > -height / 3) {
          scrollTo(0);
        } else if (translateY.value < -height / 1.5) {
          scrollTo(MAX_TRANSLATE_Y);
        }
      });

    const rBottomStyle = useAnimatedStyle(() => {
      const borderRadius = interpolate(
        translateY.value,
        [MAX_TRANSLATE_Y + 50, MAX_TRANSLATE_Y],
        [8, 5]
        //   Extrapolate.CLAMP
      );
      return {
        borderRadius,
        transform: [{ translateY: translateY.value }],
      };
    });

    useEffect(() => {
      translateY.value = withSpring(-height / 3, { damping: 50 });
    }, []);
    return (
      <GestureDetector gesture={panGesture}>
        <Animated.View style={[styles.container, rBottomStyle]}>
          <View style={styles.line} />
        </Animated.View>
      </GestureDetector>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    width,
    position: "absolute",
    height: height,
    top: height,
    backgroundColor: "white",
    borderRadius: 20,
  },
  line: {
    width: 50,
    height: 5,
    backgroundColor: "gray",
    borderRadius: 5,
    margin: 10,
    alignSelf: "center",
  },
});

export default BottomSheet;
