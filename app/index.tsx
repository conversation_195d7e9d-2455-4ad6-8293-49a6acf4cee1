import BottomSheet, { BottomSheetRefProps } from "@/components/bottom-sheet";
import React, { useCallback, useRef } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const Home = () => {
  const bottomRef = useRef<BottomSheetRefProps>(null);
  const handlePress = useCallback(() => {
    bottomRef.current?.scrollTo(-100);
  }, []);
  return (
    <SafeAreaView style={styles.container}>
      <View>
        <Text>Home</Text>
      </View>
      <TouchableOpacity style={styles.button} onPress={handlePress}>
        <Text style={{ color: "white" }}>Home</Text>
      </TouchableOpacity>
      <BottomSheet ref={bottomRef} />
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  button: {
    backgroundColor: "blue",
    padding: 10,
    borderRadius: 10,
  },
});
export default Home;
